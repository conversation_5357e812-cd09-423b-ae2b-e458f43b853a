import 'package:bloomg_flutter/auth/cubit/forgot_password_cubit.dart';
import 'package:bloomg_flutter/auth/models/email.dart';
import 'package:bloomg_flutter/auth/repository/auth_repository.dart';
import 'package:bloomg_flutter/core/di/injection.dart';
import 'package:bloomg_flutter/shared/constants/app_colors.dart';
import 'package:bloomg_flutter/shared/constants/app_dimensions.dart';
import 'package:bloomg_flutter/shared/constants/app_text_styles.dart';
import 'package:bloomg_flutter/shared/navigation/auth_navigation.dart';
import 'package:bloomg_flutter/shared/widgets/widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ForgotPasswordPage extends StatelessWidget {
  const ForgotPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Bloc<PERSON>rovider(
        create: (_) => ForgotPasswordCubit(getIt<AuthRepository>()),
        child: const ForgotPasswordForm(),
      ),
    );
  }
}

class ForgotPasswordForm extends StatefulWidget {
  const ForgotPasswordForm({super.key});

  @override
  State<ForgotPasswordForm> createState() => _ForgotPasswordFormState();
}

class _ForgotPasswordFormState extends State<ForgotPasswordForm> {
  final _emailController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _emailController.addListener(() {
      context.read<ForgotPasswordCubit>().emailChanged(_emailController.text);
    });
  }

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ForgotPasswordCubit, ForgotPasswordState>(
      listener: (context, state) {
        if (state.status.isSubmissionFailure) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              SnackBar(
                content: Text(state.errorMessage ?? 'Password Reset Failure'),
                backgroundColor: AppColors.error,
              ),
            );
        }
        if (state.status.isSubmissionSuccess) {
          ScaffoldMessenger.of(context)
            ..hideCurrentSnackBar()
            ..showSnackBar(
              const SnackBar(
                content: Text('Password reset instructions sent!'),
                backgroundColor: AppColors.success,
              ),
            );
        }
      },
      child: SafeArea(
        child: Padding(
          padding:
              const EdgeInsets.symmetric(horizontal: AppDimensions.paddingL),
          child: Column(
            children: [
              const SizedBox(height: AppDimensions.spacingHuge),
              const BloomgLogo(),
              const SizedBox(height: AppDimensions.spacingMassive),
              // Forgot Password Form
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Forgot Password',
                      style: AppTextStyles.heading1,
                    ),
                    const SizedBox(height: AppDimensions.spacingL),
                    const Text(
                      "Enter your email address and we'll send you "
                      'instructions to reset your password.',
                      style: AppTextStyles.bodyMedium,
                    ),
                    const SizedBox(height: AppDimensions.spacingXXXL),
                    // Forgot Password Form Container
                    AuthFormContainer(
                      child: Form(
                        key: _formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Email Field
                            BlocBuilder<ForgotPasswordCubit,
                                ForgotPasswordState>(
                              buildWhen: (previous, current) =>
                                  previous.email != current.email ||
                                  previous.emailTouched != current.emailTouched,
                              builder: (context, state) {
                                return ValidatedAuthFormField(
                                  label: 'Your Email Address',
                                  controller: _emailController,
                                  keyboardType: TextInputType.emailAddress,
                                  onFocusChange: ({required bool hasFocus}) {
                                    if (hasFocus) {
                                      context
                                          .read<ForgotPasswordCubit>()
                                          .emailTouched();
                                    }
                                  },
                                  errorText: state.email.error?.message,
                                  showError: state.emailTouched &&
                                      state.email.isNotValid,
                                );
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacingXXL),
                            // Send Reset Instructions Button
                            BlocBuilder<ForgotPasswordCubit,
                                ForgotPasswordState>(
                              buildWhen: (previous, current) =>
                                  previous.status != current.status,
                              builder: (context, state) {
                                return AuthButton(
                                  text: 'Send Reset Instructions',
                                  isLoading:
                                      state.status.isSubmissionInProgress,
                                  onPressed: state.status.isValidated
                                      ? () => context
                                          .read<ForgotPasswordCubit>()
                                          .sendPasswordResetEmail()
                                      : null,
                                );
                              },
                            ),
                            const SizedBox(height: AppDimensions.spacingXL),
                            // Back to Login Link
                            Center(
                              child: TextButton(
                                onPressed: () => AuthNavigation.back(context),
                                child: const Text(
                                  'Back to Login',
                                  style: AppTextStyles.linkPlain,
                                ),
                              ),
                            ),
                            const SizedBox(height: AppDimensions.spacingS),
                            // Create Account Link
                            Center(
                              child: TextButton(
                                onPressed: () =>
                                    AuthNavigation.toCreateAccount(context),
                                child: const Text(
                                  "Don't have an account? Create one",
                                  style: AppTextStyles.linkPlain,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Spacer(),
                    // Support Information
                    const SupportFooter(),
                    const SizedBox(height: AppDimensions.spacingXXXL),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
